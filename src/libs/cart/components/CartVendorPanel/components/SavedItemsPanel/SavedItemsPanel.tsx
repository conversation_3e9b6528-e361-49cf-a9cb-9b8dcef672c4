import { Button } from '@/libs/ui/Button/Button';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { SavedItemType } from '@/types';
import { SavedItemsItem } from './components/SavedItemsItem/SavedItemsItem';
import { useRemoveSavedItem } from '@/libs/cart/hooks/useRemoveSavedItem';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';

interface SavedItemsPanelProps {
  savedItems: SavedItemType[];
  startOpen: boolean;
}

export const SavedItemsPanel = ({
  savedItems,
  startOpen,
}: SavedItemsPanelProps) => {
  const { addToCart, offersMapData } = useCartStore();
  const { mutate: handleDeleteFromSavedItems } = useRemoveSavedItem();

  const handleMoveAllToCart = () => {
    const items = savedItems.map((item) => {
      const quantityOnCart = offersMapData[item.productOffer.id]?.quantity ?? 0;
      return {
        productOfferId: item.productOffer.id,
        quantity: item.quantity + quantityOnCart,
      };
    });

    addToCart({
      offers: items,
      onError: (message: string) => {
        console.error('Failed to add item to cart:', message);
      },
    });

    handleDeleteFromSavedItems(savedItems.map((item) => item.id));
  };

  return (
    <CollapsiblePanel
      key={startOpen ? 'open' : 'closed'}
      startOpen={startOpen}
      header={
        <div className="flex w-full items-center justify-between pr-16 pl-4">
          <h4 className="text-sm font-medium">
            Saved Items ({savedItems?.length || 0})
          </h4>
          <Button variant="unstyled" onClick={handleMoveAllToCart}>
            <span className="text-xs font-medium text-[#447bfd]">
              Move all to cart
            </span>
          </Button>
        </div>
      }
      content={
        <div className="bg-[#FAFAFA] p-4">
          <div className="border-1 border-black/[0.04] bg-[#fff] p-4">
            {savedItems?.map((savedItem) => (
              <SavedItemsItem key={savedItem.id} savedItem={savedItem} />
            ))}
          </div>
        </div>
      }
    />
  );
};
