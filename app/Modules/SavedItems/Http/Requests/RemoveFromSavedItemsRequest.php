<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

final class RemoveFromSavedItemsRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->account->clinics->contains(fn ($clinic) => $clinic->id === $this->clinicId());
    }

    public function rules(): array
    {
        return [
            'saved_item_ids' => 'required|array|min:1',
            'saved_item_ids.*' => 'required|string|uuid|exists:saved_items,id,clinic_id,'.$this->clinicId(),
        ];
    }

    public function messages(): array
    {
        return [
            'saved_item_ids.required' => 'Saved item eeIDs are required.',
            'saved_item_ids.array' => 'Saved item IDs must be an array.',
            'saved_item_ids.min' => 'At least one saved item ID is required.',
            'saved_item_ids.*.required' => 'Each saved item ID is required.',
            'saved_item_ids.*.uuid' => 'Each saved item ID must be a valid UUID.',
            'saved_item_ids.*.exists' => 'One or more saved items do not exist or do not belong to your clinic.',
        ];
    }
}
