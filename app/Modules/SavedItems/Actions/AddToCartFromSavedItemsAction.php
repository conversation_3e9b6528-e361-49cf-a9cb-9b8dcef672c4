<?php

declare(strict_types=1);

namespace App\Modules\SavedItems\Actions;

use App\Modules\Cart\Actions\GetCartAction;
use App\Modules\Cart\Services\ProductOfferQueryService;
use App\Modules\SavedItems\Models\SavedItem;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

final class AddToCartFromSavedItemsAction
{
    public function __construct(
        private readonly GetCartAction $getCartAction,
        private readonly ProductOfferQueryService $productOfferQueryService
    ) {}

    public function handle(array $savedItemIds): Collection
    {
        $savedItems = SavedItem::with(['clinic.cart'])
            ->whereIn('id', $savedItemIds)
            ->get();

        $clinic = $savedItems->first()?->clinic;
        $cart = $clinic?->cart;
        if (! $cart) {
            $cart = $clinic->cart()->create();
        }

        // Get product offer IDs and load them with proper clinic pricing
        $productOfferIds = $savedItems->pluck('product_offer_id')->unique()->values()->toArray();
        $productOffers = $this->productOfferQueryService->getProductOffersForCart($clinic, $productOfferIds);

        foreach ($savedItems as $savedItem) {
            try {
                // Use the properly loaded ProductOffer with clinic pricing
                $productOffer = $productOffers->get($savedItem->product_offer_id);

                if (! $productOffer) {
                    continue; // Skip if product offer not found or not accessible
                }

                $quantity = $savedItem->quantity;

                $cartItem = $cart->items()->where('product_offer_id', $productOffer->id)->first();

                if ($cartItem) {
                    $cart->updateItem($productOffer, ['quantity' => $cartItem->quantity + $quantity]);
                } else {
                    $cart->addItem($productOffer, $quantity);
                }

                $savedItem->delete();
            } catch (Exception $e) {
                Log::error('Failed to add item to saved items', [
                    'clinic_id' => $clinic->id,
                    'saved_item_id' => $savedItem->id,
                    'error' => $e->getMessage(),
                ]);
                continue;
            }
        }

        $this->getCartAction->forgetCache($clinic);

        return $savedItems;
    }
}
